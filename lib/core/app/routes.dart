import 'package:app/core/services/logger/app_logger.dart';
import 'package:app/features/auth/providers/auth_provider.dart';
import 'package:app/features/auth/screens/enter_email_screen.dart';
import 'package:app/features/auth/screens/signin/signin_login_code.dart';
import 'package:app/features/auth/screens/signin/signin_new_password.dart';
import 'package:app/features/auth/screens/signin/signin_password.dart';
import 'package:app/features/auth/screens/signup/signup_email.dart';
import 'package:app/features/auth/screens/signup/signup_username.dart';
import 'package:app/features/business/business_screen.dart';
import 'package:app/features/general/general_screen.dart';
import 'package:app/features/home/<USER>';
import 'package:app/features/matcher/matcher_screen.dart';
import 'package:app/features/pockets/pockets_screen.dart';
import 'package:app/features/search/search_screen.dart';
import 'package:app/features/settings/profile/profile_screen.dart';
import 'package:app/features/settings/settings_screen.dart';
import 'package:app/features/settings/share_profile_screen/share_profile_screen.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:talker_flutter/talker_flutter.dart';

part 'routes.g.dart';

/// Routes for the app
class Routes {
  // Root routes
  static const String root = '/';
  static const String shareProfile = '/share-profile';

  // Auth routes
  static const String auth = '/auth';
  static const String authSignInPassword =
      '/auth/${_RouteSegments.authSignInPassword}';
  static const String authSignInLoginCode =
      '/auth/${_RouteSegments.authSignInLoginCode}';
  static const String authSignInNewPassword =
      '/auth/${_RouteSegments.authSignInNewPassword}';
  static const String authSignUpUsername =
      '/auth/${_RouteSegments.authSignUpUsername}';
  static const String authSignUpEmail =
      '/auth/${_RouteSegments.authSignUpEmail}';
  // Main tab routes
  static const String home = '/home';
  static const String search = '/search';
  static const String matcher = '/matcher';
  static const String pockets = '/pockets';
  static const String business = '/business';

  // Home routes
  static const String homeSettings = '/home/<USER>';
  static const String homeProfile = '/home/<USER>';
}

class _RouteSegments {
  static const String authSignInPassword = 'sign-in/password';
  static const String authSignInLoginCode = 'sign-in/login-code';
  static const String authSignInNewPassword = 'sign-in/new-password';
  static const String authSignUpUsername = 'sign-up/username';
  static const String authSignUpEmail = 'sign-up/email';
  static const String homeSettings = 'settings';
  static const String homeProfile = 'profile';
}

final _rootNavigatorKey = GlobalKey<NavigatorState>();
final _authNavigatorKey = GlobalKey<NavigatorState>();
final _homeNavigatorKey = GlobalKey<NavigatorState>();
final _searchNavigatorKey = GlobalKey<NavigatorState>();
final _matcherNavigatorKey = GlobalKey<NavigatorState>();
final _pocketsNavigatorKey = GlobalKey<NavigatorState>();
final _businessNavigatorKey = GlobalKey<NavigatorState>();

/// Router configuration provider for the app using GoRouter
@riverpod
GoRouter router(Ref ref) {
  final authState = ref.watch(authProvider);

  return GoRouter(
    initialLocation: Routes.root,
    navigatorKey: _rootNavigatorKey,
    observers: [TalkerRouteObserver(log.talker)],
    redirect: (context, state) {
      final auth = authState;
      final isGoingToAuth = state.uri.path.startsWith('/auth');
      final isGoingToRoot = state.uri.path == '/';

      // Don't redirect while loading
      if (auth.isLoading) {
        return null;
      }

      // If not authenticated and not going to auth, redirect to auth
      if (!auth.isAuthenticated && !isGoingToAuth) {
        return Routes.auth;
      }

      // If authenticated and going to auth or root, redirect to home
      if (auth.isAuthenticated && (isGoingToAuth || isGoingToRoot)) {
        return Routes.home;
      }

      return null;
    },
    routes: [
      GoRoute(
        path: Routes.root,
        builder: (context, state) => const Scaffold(
          body: Center(child: CircularProgressIndicator.adaptive()),
        ),
      ),
      StatefulShellRoute.indexedStack(
        builder: (context, state, navigationShell) {
          return Scaffold(body: navigationShell);
        },
        branches: [
          StatefulShellBranch(
            navigatorKey: _authNavigatorKey,
            routes: [
              GoRoute(
                path: Routes.auth,
                builder: (context, state) => const AuthEnterEmailScreen(),
                routes: [
                  GoRoute(
                    path: _RouteSegments.authSignInPassword,
                    builder: (context, state) =>
                        const AuthSignInPasswordScreen(),
                  ),
                  GoRoute(
                    path: _RouteSegments.authSignInLoginCode,
                    builder: (context, state) =>
                        const AuthSignInLoginCodeScreen(),
                  ),
                  GoRoute(
                    path: _RouteSegments.authSignInNewPassword,
                    builder: (context, state) =>
                        const AuthSignInNewPasswordScreen(),
                  ),
                  GoRoute(
                    path: _RouteSegments.authSignUpUsername,
                    builder: (context, state) =>
                        const AuthSignUpUsernameScreen(),
                  ),
                  GoRoute(
                    path: _RouteSegments.authSignUpEmail,
                    builder: (context, state) => const AuthSignUpEmailScreen(),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
      StatefulShellRoute.indexedStack(
        builder: (context, state, navigationShell) {
          return MainNavigationScaffold(navigationShell: navigationShell);
        },
        branches: [
          StatefulShellBranch(
            navigatorKey: _homeNavigatorKey,
            routes: [
              GoRoute(
                path: Routes.home,
                builder: (context, state) => const HomeScreen(),
                routes: [
                  GoRoute(
                    path: _RouteSegments.homeSettings,
                    builder: (context, state) => const SettingsScreen(),
                  ),
                  GoRoute(
                    path: _RouteSegments.homeProfile,
                    builder: (context, state) => const ProfileScreen(),
                  ),
                ],
              ),
            ],
          ),
          StatefulShellBranch(
            navigatorKey: _searchNavigatorKey,
            routes: [
              GoRoute(
                path: Routes.search,
                builder: (context, state) => const SearchScreen(),
              ),
            ],
          ),
          StatefulShellBranch(
            navigatorKey: _matcherNavigatorKey,
            routes: [
              GoRoute(
                path: Routes.matcher,
                builder: (context, state) => const MatcherScreen(),
              ),
            ],
          ),
          StatefulShellBranch(
            navigatorKey: _pocketsNavigatorKey,
            routes: [
              GoRoute(
                path: Routes.pockets,
                builder: (context, state) => const PocketsScreen(),
              ),
            ],
          ),
          StatefulShellBranch(
            navigatorKey: _businessNavigatorKey,
            routes: [
              GoRoute(
                path: Routes.business,
                builder: (context, state) => const BusinessScreen(),
              ),
            ],
          ),
        ],
      ),
      GoRoute(
        path: Routes.shareProfile,
        parentNavigatorKey: _rootNavigatorKey,
        builder: (context, state) => const ShareProfileScreen(),
      ),
    ],
  );
}
