import 'package:app/common/theme/extra_colors.dart' show ExtraColors;
import 'package:flutter/material.dart'
    show
        BuildContext,
        Color,
        ColorScheme,
        MediaQuery,
        TextTheme,
        Theme,
        ThemeData;

extension ContextExtensions on BuildContext {
  double get width => MediaQuery.of(this).size.width;
  double get height => MediaQuery.of(this).size.height;

  ThemeData get theme => Theme.of(this);
  TextTheme get typo => Theme.of(this).textTheme;
  ColorScheme get colors => Theme.of(this).colorScheme;
  ExtraColors get xColors => Theme.of(this).extension<ExtraColors>()!;

  double get bottomPadding => MediaQuery.of(this).viewInsets.bottom;
}

extension ColorsExt on Color {}

extension IterableExt<T> on Iterable<T> {
  T? firstWhereOrNull(bool Function(T) test) {
    for (final element in this) {
      if (test(element)) {
        return element;
      }
    }
    return null;
  }
}

extension StringExt on String {
  String capitalize() {
    if (isEmpty || length == 1) {
      return this;
    }

    return '${this[0].toUpperCase()}${substring(1)}';
  }

  String truncate(int length) {
    return this.length <= length ? this : '${substring(0, length - 3)}...';
  }
}
