import 'package:app/core/globals.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class AuthHeader extends StatelessWidget {
  const AuthHeader(
      {required this.title,
      required this.img,
      this.subtitle,
      this.subtitleSmall = false,
      super.key,});

  final String title;
  final String? subtitle;
  final Widget img;
  final bool subtitleSmall;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Stack(
          children: [
            Positioned(left: 0, child: _buildBackButton(context)),
            Center(child: img),
          ],
        ),
        Gap.xxl,
        Text(title, style: context.typo.displayLarge),
        if (subtitle != null)
          Text(
            subtitle!,
            style: subtitleSmall
                ? context.typo.bodySmall
                    ?.copyWith(color: context.xColors.brandQuaternary50)
                : context.typo.displayLarge
                    ?.copyWith(color: context.xColors.brandQuaternary50),
          ),
      ],
    );
  }

  Widget _buildBackButton(BuildContext context) {
    final canPop = GoRouter.of(context).canPop();

    if (!canPop) {
      return const SizedBox(height: 72);
    }

    return SizedBox(
      height: 72,
      child: Row(
        children: [
          GestureDetector(
            onTap: context.pop,
            child: Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: context.xColors.brandQuinary100,
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(100),
                  bottomRight: Radius.circular(100),
                ),
              ),
              child: Icon(
                Icons.adaptive.arrow_back_rounded,
                color: context.theme.colorScheme.onPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
