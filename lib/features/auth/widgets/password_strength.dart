import 'package:app/common/theme/spacing.dart';
import 'package:app/core/helpers/extensions/context.dart';
import 'package:flutter/material.dart';

enum PasswordStrength { weak, medium, strong }

class PasswordStrengthIndicator extends StatelessWidget {
  const PasswordStrengthIndicator({
    required this.password,
    required this.strength,
    super.key,
  });

  final String password;
  final PasswordStrength strength;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildStrengthBars(context, password),
        Gap.custom(10),
        _buildRequirementsText(context, password),
      ],
    );
  }

  Widget _buildStrengthBars(BuildContext context, String password) {
    final emptyColor = context.xColors.brandQuaternary15;

    Color getBarColor(int barIndex) {
      if (password.isEmpty) return emptyColor;

      switch (strength) {
        case PasswordStrength.weak:
          return barIndex == 0 ? context.xColors.error : emptyColor;
        case PasswordStrength.medium:
          return barIndex <= 1 ? context.xColors.warning : emptyColor;
        case PasswordStrength.strong:
          return context.xColors.success;
      }
    }

    return Row(
      children: [
        Expanded(
          child: Container(
            height: 6,
            decoration: BoxDecoration(
              color: getBarColor(0),
              borderRadius: BorderRadius.circular(3),
            ),
          ),
        ),
        const SizedBox(width: 6),
        Expanded(
          child: Container(
            height: 6,
            decoration: BoxDecoration(
              color: getBarColor(1),
              borderRadius: BorderRadius.circular(3),
            ),
          ),
        ),
        const SizedBox(width: 6),
        Expanded(
          child: Container(
            height: 6,
            decoration: BoxDecoration(
              color: getBarColor(2),
              borderRadius: BorderRadius.circular(3),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRequirementsText(BuildContext context, String password) {
    var hasMinLength = password.length >= 8;
    var hasLetter = password.contains(RegExp('[a-zA-Z]'));
    var hasNumber = password.contains(RegExp('[0-9]'));
    var hasSpecial = password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));

    // If all ok
    if (hasMinLength && hasLetter && hasNumber && hasSpecial) {
      return Text(
        'Nice & strong!',
        style: context.typo.bodySmall?.copyWith(
          color: context.xColors.brandQuaternary50,
        ),
      );
    }

    if (password.isEmpty) {
      hasMinLength = true;
      hasLetter = true;
      hasNumber = true;
      hasSpecial = true;
    }

    return RichText(
      text: TextSpan(
        style: context.typo.bodySmall?.copyWith(
          color: context.xColors.brandQuaternary50,
        ),
        children: [
          const TextSpan(text: 'Use a minimum of '),
          TextSpan(
            text: '8 characters',
            style: TextStyle(
              fontWeight: hasMinLength ? FontWeight.normal : FontWeight.bold,
            ),
          ),
          const TextSpan(text: ', including '),
          TextSpan(
            text: 'letters',
            style: TextStyle(
              fontWeight: hasLetter ? FontWeight.normal : FontWeight.bold,
            ),
          ),
          const TextSpan(text: ', at least one '),
          TextSpan(
            text: 'special character',
            style: TextStyle(
              fontWeight: hasSpecial ? FontWeight.normal : FontWeight.bold,
            ),
          ),
          const TextSpan(text: ', and one '),
          TextSpan(
            text: 'number',
            style: TextStyle(
              fontWeight: hasNumber ? FontWeight.normal : FontWeight.bold,
            ),
          ),
          const TextSpan(text: '.'),
        ],
      ),
    );
  }

  static PasswordStrength getPasswordStrength(String password) {
    final hasMinLength = password.length >= 8;
    final hasLetter = password.contains(RegExp('[a-zA-Z]'));
    final hasNumber = password.contains(RegExp('[0-9]'));
    final hasSpecial = password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));

    // All criteria must be met for strong password
    if (hasMinLength && hasLetter && hasNumber && hasSpecial) {
      return PasswordStrength.strong;
    }

    // Count how many criteria are met
    var criteriaCount = 0;
    if (hasMinLength) criteriaCount++;
    if (hasLetter) criteriaCount++;
    if (hasNumber) criteriaCount++;
    if (hasSpecial) criteriaCount++;

    if (criteriaCount >= 2) return PasswordStrength.medium;
    return PasswordStrength.weak;
  }
}
