import 'package:app/common/widgets/button.dart';
import 'package:app/common/widgets/pin_theme.dart';
import 'package:app/core/globals.dart';
import 'package:app/core/helpers/extensions/widget.dart';
import 'package:app/features/auth/widgets/header.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:pinput/pinput.dart';

class AuthSignInLoginCodeScreen extends ConsumerStatefulWidget {
  const AuthSignInLoginCodeScreen({super.key});

  @override
  ConsumerState<AuthSignInLoginCodeScreen> createState() => _AuthSignInLoginCodeScreenState();
}

class _AuthSignInLoginCodeScreenState extends ConsumerState<AuthSignInLoginCodeScreen> {
  final FocusNode _focusNode = FocusNode();
  final TextEditingController _pinController = TextEditingController();

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) => _focusNode.requestFocus());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: Column(
                children: [
                  Gap.xxl,
                  AuthHeader(
                    title: 'Enter Reset code',
                    subtitle: 'This has been sent to your email',
                    img: Image.asset(
                      BadgeImages.mail,
                      width: 72,
                      height: 72,
                      fit: BoxFit.cover,
                    ).circle(),
                    subtitleSmall: true,
                  ),
                  Gap.xxl,
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: Spacing.xl),
                    child: Pinput(
                      controller: _pinController,
                      length: 6,
                      focusNode: _focusNode,
                      defaultPinTheme: pinTheme(context),
                      onCompleted: print,
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: Spacing.scaffoldInsets.copyWith(bottom: context.bottomPadding),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Gap.sm,
                  Button.gradient(
                    text: 'Continue',
                    onPressed: () {
                      context.push(Routes.authSignInNewPassword);
                    },
                  ),
                  Gap.sm,
                  Padding(
                    padding: const EdgeInsets.all(Spacing.lg),
                    child: Text(
                      'Resend code?',
                      style: context.typo.bodyMedium?.copyWith(
                        color: context.xColors.brandPrimary100,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                  Gap.sm,
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _focusNode.dispose();
    _pinController.dispose();
    super.dispose();
  }
}
