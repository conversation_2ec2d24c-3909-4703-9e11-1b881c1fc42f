import 'package:app/common/widgets/button.dart';
import 'package:app/common/widgets/input_field.dart';
import 'package:app/core/globals.dart';
import 'package:app/core/helpers/extensions/widget.dart';
import 'package:app/features/auth/widgets/header.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';

class AuthSignInPasswordScreen extends ConsumerStatefulWidget {
  const AuthSignInPasswordScreen({super.key});

  @override
  ConsumerState<AuthSignInPasswordScreen> createState() =>
      _AuthSignInPasswordScreenState();
}

class _AuthSignInPasswordScreenState
    extends ConsumerState<AuthSignInPasswordScreen> {
  late final FocusNode _focusNode;
  late final TextEditingController _controller;

  bool _isPasswordVisible = false;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
    _controller = TextEditingController();
  }

  @override
  void dispose() {
    _focusNode.dispose();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            Gap.xxl,
            Expanded(
              child: AuthHeader(
                title: 'Hey Jillian!',
                subtitle: 'Welcome back',
                img: Image.asset(
                  Images.profile,
                  width: 72,
                  height: 72,
                  fit: BoxFit.cover,
                ).circle(),
              ),
            ),
            Padding(
              padding: Spacing.scaffoldInsets
                  .copyWith(bottom: context.bottomPadding),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  InputField(
                    name: 'password',
                    labelText: 'Password',
                    keyboardType: TextInputType.visiblePassword,
                    obscureText: !_isPasswordVisible,
                    autoFocus: true,
                    suffixIcon: IconButton(
                      onPressed: () {
                        setState(() {
                          _isPasswordVisible = !_isPasswordVisible;
                        });
                      },
                      icon: SvgPicture.asset(
                        _isPasswordVisible ? SvgIcons.eye : SvgIcons.eyeClosed,
                      ),
                    ),
                  ),
                  Gap.sm,
                  Button.gradient(text: 'Continue', onPressed: () {}),
                  Gap.sm,
                  Padding(
                    padding: const EdgeInsets.all(Spacing.lg),
                    child: GestureDetector(
                      onTap: () => context.push(Routes.authSignInLoginCode),
                      child: Text(
                        'Forgot Password?',
                        style: context.typo.bodyMedium?.copyWith(
                          color: context.xColors.brandPrimary100,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ),
                  ),
                  Gap.sm,
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
