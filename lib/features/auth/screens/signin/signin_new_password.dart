import 'package:app/common/widgets/button.dart';
import 'package:app/common/widgets/input_field.dart';
import 'package:app/core/globals.dart';
import 'package:app/core/helpers/extensions/widget.dart';
import 'package:app/features/auth/providers/auth_provider.dart' show authProvider;
import 'package:app/features/auth/widgets/header.dart';
import 'package:app/features/auth/widgets/password_strength.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';

class AuthSignInNewPasswordScreen extends ConsumerStatefulWidget {
  const AuthSignInNewPasswordScreen({super.key});

  @override
  ConsumerState<AuthSignInNewPasswordScreen> createState() => _AuthSignInNewPasswordScreenState();
}

class _AuthSignInNewPasswordScreenState extends ConsumerState<AuthSignInNewPasswordScreen> {
  final TextEditingController _controller = TextEditingController();

  bool _isPasswordVisible = false;
  bool _isSaveButtonEnabled = false;
  bool _isSaveButtonLoading = false;
  bool _isSaveButtonSuccess = false;

  String _password = '';

  @override
  void initState() {
    super.initState();
    _controller.addListener(() {
      setState(() {
        _password = _controller.text;
        _isSaveButtonEnabled = PasswordStrengthIndicator.getPasswordStrength(_password) == PasswordStrength.strong;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            Gap.xxl,
            Expanded(
              child: Column(
                children: [
                  AuthHeader(
                    title: 'Choose a password',
                    img: Image.asset(
                      BadgeImages.lock,
                      width: 72,
                      height: 72,
                      fit: BoxFit.cover,
                    ).circle(),
                  ),
                  Gap.xxl,
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 36),
                    child: Column(
                      children: [
                        InputField(
                          name: 'password',
                          controller: _controller,
                          keyboardType: TextInputType.visiblePassword,
                          obscureText: !_isPasswordVisible,
                          autoFocus: true,
                          suffixIcon: IconButton(
                            onPressed: () {
                              setState(() {
                                _isPasswordVisible = !_isPasswordVisible;
                              });
                            },
                            icon: SvgPicture.asset(
                              _isPasswordVisible ? SvgIcons.eye : SvgIcons.eyeClosed,
                            ),
                          ),
                        ),
                        Gap.sm,
                        PasswordStrengthIndicator(
                          password: _password,
                          strength: PasswordStrengthIndicator.getPasswordStrength(
                            _password,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: Spacing.scaffoldInsets.copyWith(bottom: context.bottomPadding),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Gap.sm,
                  Button.gradient(
                    text: _isSaveButtonSuccess ? 'Password saved' : 'Save password',
                    onPressed: () {
                      setState(() {
                        _isSaveButtonLoading = true;
                      });

                      Future.delayed(const Duration(seconds: 2), () {
                        setState(() {
                          _isSaveButtonLoading = false;
                          _isSaveButtonSuccess = true;
                        });

                        Future.delayed(const Duration(seconds: 1), () {
                          setState(() {
                            ref.read(authProvider.notifier).login(
                                  email: '<EMAIL>',
                                  loginCode: '123456',
                                );

                            if (mounted) {
                              context.go(Routes.home);
                            }
                          });
                        });
                      });
                    },
                    isLoading: _isSaveButtonLoading,
                    isSuccess: _isSaveButtonSuccess,
                    enabled: _isSaveButtonEnabled,
                  ),
                  Gap.sm,
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
