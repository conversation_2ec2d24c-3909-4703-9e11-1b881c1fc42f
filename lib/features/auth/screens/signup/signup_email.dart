import 'package:app/common/widgets/button.dart';
import 'package:app/common/widgets/input_field.dart';
import 'package:app/core/globals.dart';
import 'package:app/core/helpers/extensions/widget.dart';
import 'package:app/features/auth/widgets/header.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

class AuthSignUpEmailScreen extends ConsumerStatefulWidget {
  const AuthSignUpEmailScreen({super.key});

  @override
  ConsumerState<AuthSignUpEmailScreen> createState() =>
      _AuthSignUpEmailScreenState();
}

class _AuthSignUpEmailScreenState extends ConsumerState<AuthSignUpEmailScreen> {
  final _isContinueButtonEnabled = true;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Safe<PERSON>rea(
        child: Column(
          children: [
            Expanded(
              child: Column(
                children: [
                  Gap.xxl,
                  AuthHeader(
                    title: 'Welcome!',
                    subtitle: 'Enter Your Email',
                    img: Image.asset(
                      BadgeImages.hand,
                      width: 72,
                      height: 72,
                      fit: BoxFit.cover,
                    ).circle(),
                  ),
                  Gap.xxl,
                  const Padding(
                    padding: EdgeInsets.symmetric(horizontal: Spacing.xl),
                    child: InputField(
                      name: 'email',
                      hintText: '<EMAIL>',
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: Spacing.scaffoldInsets
                  .copyWith(bottom: context.bottomPadding),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Gap.sm,
                  Button.gradient(
                    text: 'Continue',
                    enabled: _isContinueButtonEnabled,
                    onPressed: () {
                      context.push(Routes.authSignInNewPassword);
                    },
                  ),
                  Gap.sm,
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
