import 'package:app/common/widgets/button.dart';
import 'package:app/common/widgets/input_field.dart';
import 'package:app/core/globals.dart';
import 'package:app/core/helpers/extensions/widget.dart';
import 'package:app/features/auth/widgets/header.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';

class AuthSignUpUsernameScreen extends ConsumerStatefulWidget {
  const AuthSignUpUsernameScreen({super.key});

  @override
  ConsumerState<AuthSignUpUsernameScreen> createState() =>
      _AuthSignUpUsernameScreenState();
}

class _AuthSignUpUsernameScreenState
    extends ConsumerState<AuthSignUpUsernameScreen> {
  final TextEditingController _usernameController = TextEditingController();

  bool _isContinueButtonEnabled = false;
  bool _isAvailable = false;

  @override
  void initState() {
    super.initState();
    _usernameController.addListener(_onUsernameChanged);
  }

  @override
  void dispose() {
    _usernameController
      ..removeListener(_onUsernameChanged)
      ..dispose();
    super.dispose();
  }

  void _onUsernameChanged() {
    final username = _usernameController.text;

    if (username.length > 4) {
      final isAvailable = _checkUsernameAvailability(username);
      setState(() {
        _isAvailable = isAvailable;
        _isContinueButtonEnabled = isAvailable;
      });
    } else {
      setState(() {
        _isAvailable = false;
        _isContinueButtonEnabled = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: Column(
                children: [
                  Gap.xxl,
                  AuthHeader(
                    title: 'Welcome!',
                    subtitle: 'Choose A Username',
                    img: Image.asset(
                      BadgeImages.hand,
                      width: 72,
                      height: 72,
                      fit: BoxFit.cover,
                    ).circle(),
                  ),
                  Gap.xxl,
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: Spacing.xl),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        InputField(
                          name: 'username',
                          hintText: 'E.g. PocketRocket',
                          controller: _usernameController,
                        ),
                        Gap.sm,
                        _buildUsernameAvailability(context),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: Spacing.scaffoldInsets
                  .copyWith(bottom: context.bottomPadding),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Gap.sm,
                  Button.gradient(
                    text: 'Continue',
                    enabled: _isContinueButtonEnabled,
                    onPressed: () {
                      context.push(Routes.authSignInNewPassword);
                    },
                  ),
                  Gap.sm,
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUsernameAvailability(BuildContext context) {
    final username = _usernameController.text;

    if (username.length <= 4) {
      return Text(
        'Your username is not case-sensitive',
        style: context.typo.bodySmall
            ?.copyWith(color: context.xColors.brandQuaternary50),
      );
    }

    return Row(
      children: [
        Gap.hCustom(4),
        SvgPicture.asset(
          _isAvailable ? SvgIcons.verified : SvgIcons.warning,
        ),
        Gap.hCustom(4),
        Text(
          _isAvailable
              ? '$username is available'
              : '$username is not available',
          style: context.typo.bodySmall?.copyWith(
            color:
                _isAvailable ? context.xColors.success : context.xColors.error,
          ),
        ),
      ],
    );
  }

  // Temporary function to check if username is available
  bool _checkUsernameAvailability(String username) {
    if (username.toLowerCase().contains('test')) {
      return false;
    }

    return true;
  }
}
