import 'package:app/common/widgets/button.dart';
import 'package:app/common/widgets/input_field.dart';
import 'package:app/core/globals.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';

class AuthEnterEmailScreen extends StatefulWidget {
  const AuthEnterEmailScreen({super.key});

  @override
  State<AuthEnterEmailScreen> createState() => _AuthEnterEmailScreenState();
}

class _AuthEnterEmailScreenState extends State<AuthEnterEmailScreen> {
  late final FocusNode _focusNode;
  late final TextEditingController _controller;

  bool _continueButtonEnabled = false;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
    _controller = TextEditingController();
  }

  @override
  void dispose() {
    _focusNode.dispose();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            Gap.xxl,
            Column(
              children: [
                SvgPicture.asset(SvgIcons.logo, height: 50),
                Gap.custom(10),
                SvgPicture.asset(SvgIcons.pocketchange),
              ],
            ),
            Expanded(
              child: SingleChildScrollView(
                padding: Spacing.scaffoldInsets.copyWith(top: 0, bottom: 0),
                child: Column(
                  children: [
                    Gap.custom(60),
                    Image.asset(Images.onboarding1),
                    Gap.xxl,
                    Text(
                      'Influence change',
                      style: context.typo.headlineLarge,
                    ),
                    Gap.lg,
                    Text(
                      'Fuel local economic growth by investing in businesses that matter the most to you.',
                      style: context.typo.bodyMedium?.copyWith(
                        color: context.xColors.brandQuaternary70,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
            Padding(
              padding: Spacing.scaffoldInsets
                  .copyWith(bottom: context.bottomPadding),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  InputField(
                    name: 'email_or_username',
                    labelText: 'Email or Username',
                    keyboardType: TextInputType.emailAddress,
                    onChanged: (value) {
                      setState(() {
                        _continueButtonEnabled = value?.isNotEmpty ?? false;

                        // Temp
                        if (value?.contains('test@') ?? false) {
                          context
                              .push(Routes.authSignUpUsername); // email entered
                        } else {
                          // context.push(Routes.authSignUpEmail); // username entered
                        }
                      });
                    },
                  ),
                  Gap.sm,
                  Button.gradient(
                    text: 'Continue',
                    enabled: _continueButtonEnabled,
                    onPressed: () {
                      context.push(Routes.authSignInPassword);
                    },
                  ),
                  Gap.xxl,
                  Text.rich(
                    TextSpan(
                      children: [
                        const TextSpan(
                          text:
                              'By registering, you are creating a\nPocketchange account and agree to our\n',
                        ),
                        TextSpan(
                          text: 'Terms',
                          style: context.typo.bodySmall?.copyWith(
                            color: context.xColors.brandPrimary100,
                          ),
                        ),
                        const TextSpan(text: ' and '),
                        TextSpan(
                          text: 'Privacy Policy',
                          style: context.typo.bodySmall?.copyWith(
                            color: context.xColors.brandPrimary100,
                          ),
                        ),
                      ],
                      style: context.typo.bodySmall?.copyWith(
                        color: context.xColors.brandQuaternary70,
                      ),
                    ),
                    textAlign: TextAlign.center,
                  ),
                  Gap.sm,
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
