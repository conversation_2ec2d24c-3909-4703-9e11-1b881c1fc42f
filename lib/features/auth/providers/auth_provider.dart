import 'package:app/core/services/storage/key_value/store_keys.dart';
import 'package:app/core/services/storage/providers/storage_providers.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'auth_provider.g.dart';

class AuthState {
  const AuthState({
    this.isAuthenticated = false,
    this.isLoading = true,
    this.token,
    this.userEmail,
  });
  final bool isAuthenticated;
  final bool isLoading;
  final String? token;
  final String? userEmail;

  AuthState copyWith({
    bool? isAuthenticated,
    bool? isLoading,
    String? token,
    String? userEmail,
  }) {
    return AuthState(
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      isLoading: isLoading ?? this.isLoading,
      token: token ?? this.token,
      userEmail: userEmail ?? this.userEmail,
    );
  }
}

@riverpod
class Auth extends _$Auth {
  @override
  AuthState build() {
    _loadAuthState();
    return const AuthState();
  }

  Future<void> _loadAuthState() async {
    try {
      final token =
          await ref.read(secureStoreProvider).read(SecureStoreKeys.authToken);
      state = state.copyWith(
        isAuthenticated: token != null,
        token: token,
        isLoading: false,
      );
      // ignore: avoid_catches_without_on_clauses Not now
    } catch (e) {
      state = state.copyWith(isLoading: false);
    }
  }

  Future<void> login({
    required String email,
    String? password,
    String? loginCode,
  }) async {
    try {
      // Simulate login - в будущем здесь будет API запрос
      final fakeToken = 'token_${DateTime.now().millisecondsSinceEpoch}';

      // Save to secure storage
      await ref
          .read(secureStoreProvider)
          .write(SecureStoreKeys.authToken, fakeToken);

      // Update state
      state = state.copyWith(
        isAuthenticated: true,
        isLoading: false,
        token: fakeToken,
        userEmail: email,
      );
      // ignore: avoid_catches_without_on_clauses Not now
    } catch (e) {
      throw Exception('Login failed');
    }
  }

  Future<void> logout() async {
    await ref.read(secureStoreProvider).write(SecureStoreKeys.authToken, null);
    state = const AuthState(isLoading: false);
  }
}
