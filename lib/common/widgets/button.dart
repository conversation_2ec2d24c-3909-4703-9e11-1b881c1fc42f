import 'package:app/core/globals.dart';
import 'package:flutter/material.dart';

class But<PERSON> extends StatelessWidget {
  const Button({
    required this.text,
    this.icon,
    this.enabled = true,
    this.onPressed,
    this.backgroundColor,
    this.textColor,
    this.useGradient = false,
    this.gradientColors,
    this.fullWidth = true,
    this.isSuccess = false,
    this.isLoading = false,
    super.key,
  });

  /// Creates a button with gradient background
  const Button.gradient({
    required this.text,
    this.icon,
    this.enabled = true,
    this.onPressed,
    this.textColor,
    this.gradientColors,
    this.fullWidth = true,
    this.isSuccess = false,
    this.isLoading = false,
    super.key,
  })  : backgroundColor = null,
        useGradient = true;

  final String text;
  final Icon? icon;
  final bool enabled;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? textColor;
  final bool useGradient;
  final List<Color>? gradientColors;
  final bool fullWidth;
  final bool isSuccess;
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    // Success state colors
    const successColor = Colors.green;
    final scaffoldBackgroundColor = context.theme.scaffoldBackgroundColor;

    final effectiveTextColor = isSuccess
        ? successColor
        : !enabled
            ? context.theme.buttonTheme.colorScheme?.onPrimary.withAlpha(100)
            : textColor ?? context.theme.buttonTheme.colorScheme?.onPrimary;

    final effectiveIconColor = isSuccess
        ? successColor
        : !enabled
            ? context.theme.buttonTheme.colorScheme?.onPrimary.withAlpha(100)
            : icon?.color ?? context.theme.buttonTheme.colorScheme?.onPrimary;

    // Determine background colors and gradients
    final colors = gradientColors ??
        [
          context.xColors.brandPrimary100,
          context.xColors.brandSecondary100,
        ];

    final effectiveBackgroundColor = !enabled
        ? (backgroundColor ?? context.xColors.brandPrimary100).withAlpha(150)
        : backgroundColor ?? context.xColors.brandPrimary100;

    return SizedBox(
      height: 48,
      width: fullWidth ? double.infinity : null,
      child: Stack(
        children: [
          // Base layer - gradient or solid color
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            decoration: BoxDecoration(
              gradient: useGradient
                  ? LinearGradient(
                      colors: !enabled
                          ? colors.map((color) => color.withAlpha(100)).toList()
                          : colors,
                    )
                  : null,
              color: useGradient ? null : effectiveBackgroundColor,
              borderRadius: BorderRadius.circular(Spacing.radiusMd),
            ),
          ),
          // Success overlay layer
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            decoration: BoxDecoration(
              color: isSuccess ? scaffoldBackgroundColor : Colors.transparent,
              border: isSuccess
                  ? Border.all(color: successColor, width: 1.5)
                  : null,
              borderRadius: BorderRadius.circular(Spacing.radiusMd),
            ),
          ),
          // Button content
          Positioned.fill(
            child: ElevatedButton(
              onPressed: !enabled || isLoading ? null : onPressed,
              style: ButtonStyle(
                backgroundColor:
                    WidgetStateProperty.all<Color>(Colors.transparent),
                shadowColor: WidgetStateProperty.all<Color>(Colors.transparent),
                shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(Spacing.radiusMd),
                  ),
                ),
                elevation: WidgetStateProperty.all<double>(0),
                padding: WidgetStateProperty.all<EdgeInsets>(
                  const EdgeInsets.symmetric(horizontal: Spacing.md),
                ),
                overlayColor: isIOS
                    ? WidgetStateProperty.all<Color>(Colors.transparent)
                    : null,
              ),
              child: AnimatedSize(
                duration: const Duration(milliseconds: 200),
                curve: Curves.easeInOut,
                child: AnimatedSwitcher(
                  duration: const Duration(milliseconds: 300),
                  transitionBuilder: (child, animation) =>
                      FadeTransition(opacity: animation, child: child),
                  child: isLoading
                      ? SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator.adaptive(
                            key: const ValueKey('loading'),
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              effectiveTextColor ?? Colors.white,
                            ),
                          ),
                        )
                      : Row(
                          key: const ValueKey('content'),
                          mainAxisSize:
                              fullWidth ? MainAxisSize.max : MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            if (icon != null) ...[
                              IconTheme(
                                data: IconThemeData(
                                  color: effectiveIconColor,
                                  size: icon?.size,
                                ),
                                child: icon ?? const SizedBox.shrink(),
                              ),
                              const SizedBox(width: 4),
                            ],
                            Text(
                              text,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyLarge
                                  ?.copyWith(
                                    color: effectiveTextColor,
                                  ),
                            ),
                          ],
                        ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
