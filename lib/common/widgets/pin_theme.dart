import 'package:app/common/theme/spacing.dart';
import 'package:app/core/helpers/extensions/context.dart';
import 'package:flutter/material.dart';
import 'package:pinput/pinput.dart';

PinTheme pinTheme(BuildContext context) => PinTheme(
      height: 60,
      width: 50,
      textStyle: context.typo.bodyMedium
          ?.copyWith(color: context.xColors.brandQuaternary70),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(Spacing.radiusMd),
        color: context.xColors.brandQuaternary5,
      ),
    );
