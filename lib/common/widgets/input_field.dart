import 'package:app/core/globals.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';

class InputFieldTheme {
  static const _radius = Spacing.radiusMd;

  static OutlineInputBorder _border(Color? color) => OutlineInputBorder(
        borderRadius: BorderRadius.circular(_radius),
        borderSide: BorderSide(color: color ?? Colors.transparent),
      );

  static InputDecoration decoration({
    required BuildContext context,
    required String? hint,
    required Widget? label,
    required bool focused,
    Widget? prefix,
    Widget? suffix,
    String? error,
  }) =>
      InputDecoration(
        isDense: true,
        filled: true,
        fillColor: context.xColors.brandQuaternary5,
        hintText: hint,
        hintStyle: context.typo.bodyMedium
            ?.copyWith(color: context.xColors.brandQuaternary50),
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 18, vertical: 13),
        prefixIcon: prefix,
        suffixIcon: suffix,
        prefixIconColor: focused
            ? context.xColors.brandQuinary100
            : context.xColors.brandQuinary100,
        floatingLabelBehavior: FloatingLabelBehavior.auto,
        label: label,
        labelStyle: const TextStyle(height: 0),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(_radius),
          borderSide: BorderSide(color: context.xColors.brandQuaternary0),
        ),
        focusedBorder: _border(context.xColors.brandQuaternary0),
        errorBorder: _border(context.xColors.brandPrimary50),
        focusedErrorBorder: _border(context.xColors.brandPrimary50),
        errorText: error,
      );
}

class InputField extends StatefulWidget {
  const InputField({
    required this.name,
    this.controller,
    this.hintText,
    this.prefixIcon,
    this.suffixIcon,
    this.errorText,
    this.labelText,
    this.keyboardType = TextInputType.text,
    this.textInputAction,
    this.validators,
    this.onChanged,
    this.onTap,
    this.onSubmitted,
    this.autofillHints = const [],
    this.autoFocus = false,
    this.maxLines = 1,
    this.disableLoseFocus = false,
    this.obscureText = false,
    super.key,
  });

  final String name;
  final TextEditingController? controller;
  final String? hintText;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final String? errorText;
  final String? labelText;
  final TextInputType keyboardType;
  final TextInputAction? textInputAction;
  final FormFieldValidator<String>? validators;
  final void Function(String?)? onChanged;
  final void Function()? onTap;
  final void Function()? onSubmitted;
  final bool autoFocus;
  final int maxLines;
  final List<String> autofillHints;
  final bool disableLoseFocus;
  final bool obscureText;

  @override
  State<InputField> createState() => _InputFieldState();
}

class _InputFieldState extends State<InputField> {
  late final FocusNode _focus = FocusNode();
  TextEditingController? _controller;

  bool get _isFloating =>
      _focus.hasFocus || (_controller?.text.isNotEmpty ?? false);

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    _focus.addListener(() => setState(() {}));
    _controller?.addListener(() => setState(() {}));
  }

  @override
  void dispose() {
    _focus.dispose();
    if (widget.controller == null) {
      _controller?.dispose();
    }
    super.dispose();
  }

  Widget _chipLabel(BuildContext context, String text) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 150),
      padding: _isFloating
          ? const EdgeInsets.symmetric(horizontal: 6, vertical: 2)
          : EdgeInsets.zero,
      decoration: BoxDecoration(
        color: _isFloating
            ? context.xColors.brandTertiary100
            : context.xColors.brandQuaternary0,
        borderRadius: BorderRadius.circular(3),
        border: Border.all(
          color: _isFloating
              ? context.xColors.brandTertiary100
              : Colors.transparent,
        ),
      ),
      child: AnimatedDefaultTextStyle(
        duration: const Duration(milliseconds: 150),
        style: (_isFloating
                ? context.typo.labelSmall
                    ?.copyWith(color: context.xColors.brandSenary100)
                : context.typo.bodyMedium
                    ?.copyWith(color: context.xColors.brandQuaternary50)) ??
            const TextStyle(),
        child: Text(text),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: FormBuilderTextField(
        name: widget.name,
        controller: _controller,
        focusNode: _focus,
        decoration: InputFieldTheme.decoration(
          context: context,
          hint: widget.hintText,
          label: widget.labelText != null
              ? _chipLabel(context, widget.labelText!)
              : null,
          focused: _focus.hasFocus,
          prefix: widget.prefixIcon,
          suffix: widget.suffixIcon,
          error: widget.errorText,
        ),
        style: context.typo.bodyMedium?.copyWith(
          color: context.xColors.brandQuaternary70,
        ),
        obscureText: widget.obscureText,
        obscuringCharacter: '*',
        keyboardType: widget.keyboardType,
        textInputAction: widget.textInputAction,
        autofillHints: widget.autofillHints,
        maxLines: widget.maxLines,
        autofocus: widget.autoFocus,
        validator: widget.validators,
        onChanged: widget.onChanged,
        onTap: widget.onTap,
        onTapOutside: (_) {
          if (!widget.disableLoseFocus) _focus.unfocus();
        },
        onSubmitted: (_) => widget.onSubmitted?.call(),
      ),
    );
  }
}
