import 'package:flutter/widgets.dart';

/// Centralized spacing values for consistent design system
abstract class Spacing {
  /// 6px
  static const double xxs = 6;

  /// 10px
  static const double xs = 10;

  /// 12px
  static const double sm = 12;

  /// 16px
  static const double md = 16;

  /// 18px
  static const double lg = 18;

  /// 20px
  static const double xl = 20;

  /// 24px
  static const double xxl = 24;

  // Common EdgeInsets
  static const EdgeInsets scaffoldInsets = EdgeInsets.all(xl);
  static const EdgeInsets cardInsets = EdgeInsets.all(md);
  static const EdgeInsets buttonInsets =
      EdgeInsets.symmetric(horizontal: xs, vertical: xs);
  static const EdgeInsets iconInsets = EdgeInsets.all(xs);

  // Icon sizes
  /// 12px
  static const double iconSm = 12;

  /// 18px
  static const double iconMd = 18;

  /// 24px
  static const double iconLg = 24;

  // Border radius
  /// 10px
  static const double radiusSm = 10;

  /// 12px
  static const double radiusMd = 12;

  /// 16px
  static const double radiusLg = 16;

  /// 24px
  static const double radiusXl = 24;
}

/// Gaps for consistent spacing between elements
abstract class Gap {
  /// 6px
  static const SizedBox xxs = SizedBox(height: Spacing.xxs);

  /// 10px
  static const SizedBox xs = SizedBox(height: Spacing.xs);

  /// 12px
  static const SizedBox sm = SizedBox(height: Spacing.sm);

  /// 16px
  static const SizedBox md = SizedBox(height: Spacing.md);

  /// 18px
  static const SizedBox lg = SizedBox(height: Spacing.lg);

  /// 20px
  static const SizedBox xl = SizedBox(height: Spacing.xl);

  /// 24px
  static const SizedBox xxl = SizedBox(height: Spacing.xxl);

  // Horizontal gaps
  /// 6px
  static const SizedBox hXxs = SizedBox(width: Spacing.xxs);

  /// 10px
  static const SizedBox hXs = SizedBox(width: Spacing.xs);

  /// 12px
  static const SizedBox hSm = SizedBox(width: Spacing.sm);

  /// 16px
  static const SizedBox hMd = SizedBox(width: Spacing.md);

  /// 18px
  static const SizedBox hLg = SizedBox(width: Spacing.lg);

  /// 20px
  static const SizedBox hXl = SizedBox(width: Spacing.xl);

  /// 24px
  static const SizedBox hXxl = SizedBox(width: Spacing.xxl);

  /// Custom
  static SizedBox custom(double height) => SizedBox(height: height);
  static SizedBox hCustom(double width) => SizedBox(width: width);
}
