import 'dart:io';

import 'package:app/gen/assets/resources.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('svg_icons assets test', () {
    expect(File(SvgIcons.arrowRight).existsSync(), isTrue);
    expect(File(SvgIcons.business).existsSync(), isTrue);
    expect(File(SvgIcons.copy).existsSync(), isTrue);
    expect(File(SvgIcons.eye).existsSync(), isTrue);
    expect(File(SvgIcons.eyeClosed).existsSync(), isTrue);
    expect(File(SvgIcons.home).existsSync(), isTrue);
    expect(File(SvgIcons.logo).existsSync(), isTrue);
    expect(File(SvgIcons.mail).existsSync(), isTrue);
    expect(File(SvgIcons.match).existsSync(), isTrue);
    expect(File(SvgIcons.pocket).existsSync(), isTrue);
    expect(File(SvgIcons.pocketchange).existsSync(), isTrue);
    expect(File(SvgIcons.search).existsSync(), isTrue);
    expect(File(SvgIcons.settings).existsSync(), isTrue);
    expect(File(SvgIcons.share).existsSync(), isTrue);
    expect(File(SvgIcons.verified).existsSync(), isTrue);
    expect(File(SvgIcons.warning).existsSync(), isTrue);
  });
}
