import 'package:app/core/app/routes.dart';
import 'package:app/core/globals.dart';
import 'package:app/core/services/logger/app_logger.dart';
import 'package:app/features/auth/providers/auth_provider.dart';
import 'package:app/gen/translations.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'core/storage_test_utils.dart';

export 'package:app/core/app/routes.dart' show Routes;

Widget prepareTestApp({String? initialLocation, ProviderContainer? container}) {
  FlutterError.onError = (FlutterErrorDetails details) {
    // test
    // ignore: only_throw_errors
    throw details.exception;
  };

  // Create test container with auth override to skip loading
  final testContainer = container ??
      ProviderContainer(
        overrides: [
          authProvider.overrideWith(TestAuthNotifier.new),
        ],
      );

  final Widget child = UncontrolledProviderScope(
    container: testContainer,
    child: Consumer(
      builder: (context, ref, child) {
        // Get router from provider like in real app
        final router = ref.watch(routerProvider);

        // Override initial location if needed
        if (initialLocation != null) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            router.go(initialLocation);
          });
        }

        return TranslationProvider(
          child: MaterialApp.router(
            localizationsDelegates: GlobalMaterialLocalizations.delegates,
            routerConfig: router,
            supportedLocales: AppLocaleUtils.supportedLocales,
          ),
        );
      },
    ),
  );

  return child;
}

Future<void> setupAll() async {
  SharedPreferences.setMockInitialValues({});

  // Initialize App.config for tests without loading environment files
  App.config = AppConfig.test();

  // Initialize logger after config is set
  AppLogger.instance;
}

/// Create a properly configured test app with bootstrapped storage
Future<Widget> prepareTestAppWithStorage({String? initialLocation}) async {
  final container = await StorageTestUtils.createBootstrappedContainer();
  return prepareTestApp(initialLocation: initialLocation, container: container);
}

// Test auth notifier that skips loading
class TestAuthNotifier extends Auth {
  @override
  AuthState build() {
    // Return authenticated state immediately for tests
    return const AuthState(
      isAuthenticated: true,
      isLoading: false,
      token: 'test_token',
      userEmail: '<EMAIL>',
    );
  }
}
